[target.aarch64-linux-android]
ar = "aarch64-linux-android-ar"
linker = "aarch64-linux-android21-clang"
rustflags = [
    "-C", "link-arg=-Wl,--allow-shlib-undefined",
    "-C", "link-arg=-Wl,--no-undefined-version",
    "-C", "link-arg=-nostdlib++",
    "-C", "link-arg=-lunwind",
    "-C", "link-arg=-nostdlib"
]

[target.armv7-linux-androideabi]
ar = "arm-linux-androideabi-ar"
linker = "armv7a-linux-androideabi19-clang"
rustflags = [
    "-C", "link-arg=-Wl,--allow-shlib-undefined",
    "-C", "link-arg=-lunwind",
    "-C", "link-arg=-nostdlib++",
    "-C", "link-arg=-nostdlib"
]

[target.i686-linux-android]
ar = "i686-linux-android-ar"
linker = "i686-linux-android19-clang"
rustflags = [
    "-C", "link-arg=-Wl,--allow-shlib-undefined",
    "-C", "link-arg=-lunwind",
    "-C", "link-arg=-nostdlib++",
    "-C", "link-arg=-nostdlib"
]

[target.x86_64-linux-android]
ar = "x86_64-linux-android-ar"
linker = "x86_64-linux-android21-clang"
rustflags = [
    "-C", "link-arg=-Wl,--allow-shlib-undefined",
    "-C", "link-arg=-lunwind",
    "-C", "link-arg=-nostdlib++",
    "-C", "link-arg=-nostdlib"
]

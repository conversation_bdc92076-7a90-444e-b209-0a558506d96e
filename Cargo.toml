[package]
name = "cert-verify-rs"
version = "0.1.0"
edition = "2021"

[lib]
name = "cert_verify_rs"
crate-type = ["staticlib"]

[features]
default = []

[dependencies]
p256 = { version = "0.9", features = ["ecdsa", "sha256"] }
ecdsa = "0.13"
sha2 = "0.9"
libc = "0.2"
# compiler_builtins = { version = "0.1", features = ["no-compiler-rt"], optional = true }

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true
opt-level = "z"
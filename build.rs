use std::env;
use std::path::PathBuf;

fn main() {
    // Set up include path for C header
    let out_path = PathBuf::from(env::var("OUT_DIR").unwrap());
    println!("cargo:include={}", out_path.display());

    // Copy header file to output directory
    let header_src = "include/cert_verify_rs.h";
    let header_dst = out_path.join("cert_verify_rs.h");

    if let Ok(content) = std::fs::read_to_string(header_src) {
        let _ = std::fs::write(header_dst, content);
    }
}
